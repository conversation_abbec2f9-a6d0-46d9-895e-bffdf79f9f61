RAPPORT D'ANALYSE AGRÉGÉE - PARTIES INDÉPENDANTES
================================================================================

Généré le : 2025-07-03 14:44:04
Architecture : Partie par partie (8 cœurs)

RÉSULTATS DÉTAILLÉS :
{
  "statistiques_globales": {
    "total_parties_analysees": 1000,
    "parties_reussies": 0,
    "parties_echouees": 1000,
    "total_mains_analysees": 66221,
    "total_manches_analysees": 60000,
    "moyenne_mains_par_partie": 66.221,
    "moyenne_manches_par_partie": 60.0,
    "entropie_moyenne": 0
  },
  "performance_metrics": {
    "total_parties_analyzed": 1000,
    "successful_analyses": 0,
    "failed_analyses": 1000,
    "analysis_duration_seconds": 1.300578,
    "parties_per_second": 0.0,
    "parallel_workers_used": 1,
    "failed_parties_details": [
      {
        "partie_id": 0,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 1,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 2,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 3,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 4,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 5,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 6,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 7,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 8,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 9,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 10,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 11,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 12,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 13,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 14,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 15,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 16,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 17,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 18,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 19,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 20,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 21,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 22,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 23,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 24,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 25,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 26,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 27,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 28,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 29,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 30,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 31,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 32,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 33,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 34,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 35,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 36,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 37,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 38,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 39,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 40,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 41,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 42,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 43,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 44,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 45,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 46,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 47,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 48,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 49,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 50,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 51,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 52,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 53,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 54,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 55,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 56,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 57,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 58,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 59,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 60,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 61,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 62,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 63,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 64,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 65,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 66,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 67,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 68,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 69,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 70,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 71,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 72,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 73,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 74,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 75,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 76,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 77,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 78,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 79,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 80,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 81,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 82,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 83,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 84,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 85,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 86,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 87,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 88,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 89,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 90,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 91,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 92,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 93,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 94,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 95,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 96,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 97,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 98,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 99,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 100,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 101,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 102,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 103,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 104,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 105,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 106,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 107,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 108,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 109,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 110,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 111,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 112,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 113,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 114,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 115,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 116,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 117,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 118,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 119,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 120,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 121,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 122,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 123,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 124,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 125,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 126,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 127,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 128,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 129,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 130,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 131,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 132,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 133,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 134,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 135,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 136,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 137,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 138,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 139,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 140,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 141,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 142,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 143,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 144,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 145,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 146,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 147,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 148,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 149,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 150,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 151,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 152,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 153,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 154,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 155,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 156,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 157,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 158,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 159,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 160,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 161,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 162,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 163,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 164,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 165,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 166,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 167,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 168,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 169,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 170,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 171,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 172,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 173,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 174,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 175,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 176,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 177,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 178,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 179,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 180,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 181,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 182,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 183,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 184,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 185,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 186,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 187,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 188,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 189,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 190,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 191,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 192,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 193,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 194,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 195,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 196,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 197,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 198,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 199,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 200,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 201,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 202,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 203,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 204,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 205,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 206,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 207,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 208,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 209,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 210,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 211,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 212,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 213,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 214,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 215,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 216,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 217,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 218,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 219,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 220,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 221,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 222,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 223,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 224,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 225,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 226,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 227,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 228,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 229,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 230,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 231,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 232,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 233,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 234,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 235,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 236,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 237,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 238,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 239,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 240,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 241,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 242,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 243,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 244,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 245,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 246,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 247,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 248,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 249,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 250,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 251,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 252,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 253,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 254,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 255,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 256,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 257,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 258,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 259,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 260,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 261,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 262,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 263,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 264,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 265,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 266,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 267,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 268,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 269,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 270,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 271,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 272,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 273,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 274,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 275,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 276,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 277,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 278,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 279,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 280,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 281,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 282,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 283,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 284,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 285,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 286,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 287,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 288,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 289,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 290,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 291,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 292,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 293,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 294,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 295,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 296,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 297,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 298,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 299,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 300,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 301,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 302,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 303,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 304,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 305,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 306,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 307,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 308,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 309,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 310,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 311,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 312,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 313,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 314,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 315,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 316,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 317,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 318,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 319,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 320,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 321,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 322,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 323,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 324,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 325,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 326,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 327,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 328,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 329,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 330,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 331,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 332,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 333,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 334,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 335,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 336,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 337,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 338,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 339,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 340,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 341,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 342,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 343,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 344,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 345,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 346,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 347,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 348,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 349,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 350,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 351,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 352,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 353,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 354,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 355,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 356,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 357,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 358,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 359,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 360,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 361,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 362,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 363,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 364,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 365,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 366,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 367,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 368,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 369,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 370,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 371,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 372,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 373,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 374,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 375,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 376,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 377,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 378,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 379,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 380,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 381,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 382,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 383,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 384,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 385,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 386,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 387,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 388,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 389,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 390,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 391,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 392,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 393,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 394,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 395,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 396,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 397,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 398,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 399,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 400,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 401,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 402,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 403,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 404,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 405,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 406,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 407,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 408,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 409,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 410,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 411,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 412,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 413,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 414,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 415,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 416,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 417,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 418,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 419,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 420,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 421,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 422,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 423,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 424,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 425,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 426,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 427,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 428,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 429,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 430,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 431,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 432,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 433,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 434,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 435,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 436,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 437,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 438,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 439,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 440,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 441,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 442,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 443,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 444,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 445,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 446,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 447,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 448,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 449,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 450,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 451,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 452,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 453,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 454,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 455,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 456,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 457,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 458,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 459,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 460,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 461,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 462,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 463,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 464,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 465,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 466,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 467,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 468,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 469,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 470,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 471,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 472,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 473,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 474,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 475,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 476,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 477,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 478,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 479,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 480,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 481,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 482,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 483,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 484,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 485,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 486,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 487,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 488,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 489,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 490,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 491,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 492,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 493,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 494,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 495,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 496,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 497,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 498,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 499,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 500,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 501,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 502,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 503,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 504,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 505,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 506,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 507,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 508,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 509,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 510,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 511,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 512,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 513,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 514,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 515,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 516,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 517,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 518,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 519,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 520,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 521,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 522,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 523,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 524,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 525,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 526,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 527,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 528,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 529,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 530,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 531,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 532,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 533,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 534,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 535,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 536,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 537,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 538,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 539,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 540,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 541,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 542,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 543,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 544,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 545,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 546,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 547,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 548,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 549,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 550,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 551,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 552,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 553,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 554,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 555,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 556,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 557,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 558,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 559,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 560,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 561,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 562,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 563,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 564,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 565,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 566,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 567,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 568,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 569,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 570,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 571,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 572,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 573,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 574,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 575,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 576,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 577,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 578,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 579,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 580,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 581,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 582,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 583,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 584,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 585,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 586,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 587,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 588,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 589,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 590,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 591,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 592,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 593,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 594,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 595,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 596,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 597,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 598,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 599,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 600,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 601,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 602,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 603,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 604,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 605,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 606,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 607,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 608,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 609,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 610,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 611,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 612,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 613,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 614,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 615,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 616,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 617,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 618,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 619,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 620,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 621,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 622,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 623,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 624,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 625,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 626,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 627,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 628,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 629,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 630,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 631,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 632,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 633,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 634,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 635,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 636,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 637,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 638,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 639,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 640,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 641,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 642,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 643,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 644,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 645,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 646,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 647,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 648,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 649,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 650,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 651,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 652,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 653,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 654,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 655,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 656,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 657,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 658,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 659,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 660,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 661,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 662,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 663,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 664,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 665,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 666,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 667,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 668,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 669,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 670,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 671,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 672,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 673,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 674,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 675,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 676,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 677,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 678,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 679,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 680,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 681,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 682,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 683,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 684,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 685,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 686,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 687,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 688,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 689,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 690,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 691,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 692,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 693,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 694,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 695,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 696,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 697,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 698,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 699,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 700,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 701,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 702,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 703,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 704,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 705,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 706,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 707,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 708,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 709,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 710,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 711,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 712,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 713,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 714,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 715,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 716,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 717,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 718,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 719,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 720,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 721,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 722,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 723,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 724,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 725,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 726,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 727,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 728,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 729,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 730,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 731,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 732,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 733,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 734,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 735,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 736,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 737,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 738,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 739,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 740,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 741,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 742,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 743,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 744,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 745,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 746,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 747,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 748,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 749,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 750,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 751,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 752,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 753,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 754,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 755,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 756,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 757,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 758,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 759,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 760,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 761,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 762,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 763,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 764,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 765,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 766,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 767,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 768,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 769,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 770,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 771,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 772,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 773,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 774,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 775,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 776,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 777,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 778,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 779,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 780,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 781,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 782,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 783,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 784,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 785,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 786,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 787,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 788,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 789,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 790,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 791,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 792,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 793,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 794,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 795,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 796,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 797,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 798,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 799,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 800,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 801,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 802,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 803,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 804,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 805,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 806,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 807,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 808,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 809,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 810,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 811,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 812,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 813,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 814,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 815,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 816,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 817,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 818,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 819,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 820,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 821,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 822,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 823,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 824,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 825,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 826,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 827,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 828,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 829,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 830,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 831,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 832,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 833,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 834,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 835,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 836,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 837,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 838,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 839,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 840,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 841,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 842,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 843,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 844,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 845,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 846,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 847,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 848,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 849,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 850,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 851,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 852,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 853,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 854,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 855,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 856,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 857,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 858,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 859,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 860,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 861,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 862,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 863,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 864,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 865,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 866,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 867,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 868,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 869,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 870,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 871,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 872,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 873,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 874,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 875,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 876,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 877,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 878,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 879,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 880,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 881,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 882,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 883,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 884,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 885,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 886,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 887,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 888,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 889,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 890,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 891,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 892,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 893,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 894,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 895,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 896,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 897,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 898,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 899,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 900,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 901,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 902,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 903,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 904,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 905,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 906,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 907,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 908,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 909,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 910,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 911,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 912,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 913,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 914,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 915,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 916,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 917,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 918,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 919,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 920,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 921,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 922,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 923,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 924,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 925,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 926,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 927,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 928,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 929,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 930,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 931,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 932,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 933,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 934,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 935,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 936,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 937,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 938,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 939,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 940,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 941,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 942,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 943,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 944,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 945,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 946,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 947,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 948,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 949,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 950,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 951,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 952,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 953,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 954,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 955,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 956,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 957,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 958,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 959,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 960,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 961,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 962,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 963,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 964,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 965,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 966,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 967,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 968,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 969,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 970,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 971,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 972,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 973,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 974,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 975,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 976,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 977,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 978,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 979,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 980,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 981,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 982,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 983,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 984,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 985,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 986,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 987,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 988,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 989,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 990,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 991,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 992,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 993,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 994,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 995,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 996,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 997,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 998,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      },
      {
        "partie_id": 999,
        "erreur": "'BaccaratMarkovAnalyzer' object has no attribute '_calculate_shannon_entropy'"
      }
    ]
  },
  "resultats_detailles": {}
}