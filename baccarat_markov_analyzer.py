#!/usr/bin/env python3
"""
ANALYSEUR PROBABILISTE AVANCÉ POUR PARTIES DE BACCARAT
======================================================

Analyse des chaînes de Markov appliquées aux données de Baccarat du fichier exemple.json
Basé sur les 97 formules mathématiques de Base_de_donnees_formules_mathematiques_Markov.txt

FORMULES IMPLÉMENTÉES :
- FORMULE 1-5 : Propriétés fondamentales et transitions
- FORMULE 8, 14 : Temps de retour et récurrence
- FORMULE 16-25 : Analyse spectrale et convergence
- FORMULE 35-36 : Théorème ergodique
- FORMULE 61-70 : Entropie et théorie de l'information
- FORMULE 79-85 : Estimation statistique

Auteur: Système d'analyse Lupasco
Date: 2025-07-03
"""

import json
import numpy as np
import pandas as pd
from scipy import linalg, stats
from scipy.sparse import csr_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

@dataclass
class BaccaratHand:
    """Structure pour une main de Baccarat"""
    main_number: int
    manche_pb_number: int
    cartes_player: List[Dict]
    cartes_banker: List[Dict]
    score_player: int
    score_banker: int
    index1: int
    index2: str
    index3: str
    index5: str
    cards_count: int
    timestamp: str

@dataclass
class MarkovAnalysisResult:
    """Résultats d'analyse de chaînes de Markov"""
    transition_matrix: np.ndarray
    stationary_distribution: np.ndarray
    eigenvalues: np.ndarray
    convergence_rate: float
    entropy_rate: float
    mixing_time: int
    return_probabilities: Dict[str, float]
    ergodic_properties: Dict[str, Any]

class BaccaratMarkovAnalyzer:
    """
    CLASSE PRINCIPALE D'ANALYSE PROBABILISTE
    ========================================
    
    Implémente les formules de chaînes de Markov pour analyser les parties de Baccarat.
    Basée sur les 97 formules mathématiques du cours de Dimitri Petritis.
    """
    
    def __init__(self, json_file_path: str):
        """
        Initialise l'analyseur avec les données JSON
        
        Args:
            json_file_path: Chemin vers le fichier exemple.json
        """
        self.json_file_path = json_file_path
        self.data = self._load_data()
        self.hands = self._parse_hands()
        self.states = self._extract_states()
        self.state_to_idx = {state: i for i, state in enumerate(self.states)}
        self.idx_to_state = {i: state for i, state in enumerate(self.states)}
        
    def _load_data(self) -> Dict:
        """Charge les données JSON"""
        with open(self.json_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _parse_hands(self) -> List[BaccaratHand]:
        """Parse les mains de Baccarat depuis les données JSON"""
        hands = []
        for partie in self.data['parties']:
            for main_data in partie['mains']:
                if main_data['main_number'] is not None:  # Exclure la main vide
                    hand = BaccaratHand(
                        main_number=main_data['main_number'],
                        manche_pb_number=main_data['manche_pb_number'],
                        cartes_player=main_data['cartes_player'],
                        cartes_banker=main_data['cartes_banker'],
                        score_player=main_data['score_player'],
                        score_banker=main_data['score_banker'],
                        index1=main_data['index1'],
                        index2=main_data['index2'],
                        index3=main_data['index3'],
                        index5=main_data['index5'],
                        cards_count=main_data['cards_count'],
                        timestamp=main_data['timestamp']
                    )
                    hands.append(hand)
        return hands
    
    def _extract_states(self) -> List[str]:
        """
        Extrait les états uniques de la chaîne de Markov
        États basés sur INDEX5 (combinaison INDEX1_INDEX2_INDEX3)
        """
        states = list(set(hand.index5 for hand in self.hands))
        return sorted(states)
    
    def get_sequence(self, attribute: str = 'index5') -> List[str]:
        """
        Obtient la séquence temporelle d'un attribut
        
        Args:
            attribute: Attribut à extraire ('index5', 'index3', 'index2', etc.)
        """
        return [getattr(hand, attribute) for hand in self.hands]
    
    # ============================================================================
    # FORMULES 1-7: PROPRIÉTÉS FONDAMENTALES DES CHAÎNES DE MARKOV
    # ============================================================================
    
    def compute_transition_matrix(self, sequence: Optional[List[str]] = None) -> np.ndarray:
        """
        FORMULE 1-2: Calcul de la matrice de transition stochastique
        
        Implémente:
        - Formule 1: Propriété faible de Markov P_{x_n, y}
        - Formule 2: Condition de stochasticité ∑_{z∈𝕏} P_{x,z} = 1
        
        Args:
            sequence: Séquence d'états (par défaut: INDEX5)
        
        Returns:
            Matrice de transition stochastique
        """
        if sequence is None:
            sequence = self.get_sequence('index5')
        
        n_states = len(self.states)
        transition_counts = np.zeros((n_states, n_states))
        
        # Compter les transitions
        for i in range(len(sequence) - 1):
            current_state = sequence[i]
            next_state = sequence[i + 1]
            
            current_idx = self.state_to_idx[current_state]
            next_idx = self.state_to_idx[next_state]
            
            transition_counts[current_idx, next_idx] += 1
        
        # Normalisation pour obtenir les probabilités (Formule 2)
        transition_matrix = np.zeros((n_states, n_states))
        for i in range(n_states):
            row_sum = transition_counts[i, :].sum()
            if row_sum > 0:
                transition_matrix[i, :] = transition_counts[i, :] / row_sum
            else:
                # État sans transition sortante - distribution uniforme
                transition_matrix[i, :] = 1.0 / n_states
        
        return transition_matrix
    
    def verify_markov_property(self, sequence: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        FORMULE 7: Vérification de l'indépendance conditionnelle
        
        Teste si ℙ(A ∩ C | B) = ℙ(A | B) ℙ(C | B)
        où A = passé, B = présent, C = futur
        """
        if sequence is None:
            sequence = self.get_sequence('index5')
        
        # Test statistique de la propriété de Markov
        # Comparaison des probabilités conditionnelles d'ordre 1 vs ordre 2
        
        transitions_order1 = {}  # P(X_{n+1} | X_n)
        transitions_order2 = {}  # P(X_{n+1} | X_{n-1}, X_n)
        
        # Ordre 1
        for i in range(len(sequence) - 1):
            current = sequence[i]
            next_state = sequence[i + 1]
            key = (current, next_state)
            transitions_order1[key] = transitions_order1.get(key, 0) + 1
        
        # Ordre 2
        for i in range(len(sequence) - 2):
            prev_state = sequence[i]
            current = sequence[i + 1]
            next_state = sequence[i + 2]
            key = (prev_state, current, next_state)
            transitions_order2[key] = transitions_order2.get(key, 0) + 1
        
        # Test chi-carré pour l'indépendance
        chi2_statistic = 0.0
        degrees_freedom = 0
        
        for (prev, curr, next_state), count2 in transitions_order2.items():
            # Probabilité attendue sous hypothèse markovienne
            count1_curr_next = transitions_order1.get((curr, next_state), 0)
            total_from_curr = sum(v for (c, n), v in transitions_order1.items() if c == curr)
            
            if total_from_curr > 0:
                expected_prob = count1_curr_next / total_from_curr
                total_from_prev_curr = sum(v for (p, c, n), v in transitions_order2.items() 
                                         if p == prev and c == curr)
                
                if total_from_prev_curr > 0:
                    expected_count = expected_prob * total_from_prev_curr
                    if expected_count > 0:
                        chi2_statistic += (count2 - expected_count) ** 2 / expected_count
                        degrees_freedom += 1
        
        p_value = 1 - stats.chi2.cdf(chi2_statistic, degrees_freedom) if degrees_freedom > 0 else 1.0
        
        return {
            'chi2_statistic': chi2_statistic,
            'degrees_freedom': degrees_freedom,
            'p_value': p_value,
            'markov_property_satisfied': p_value > 0.05,  # Seuil 5%
            'transitions_order1_count': len(transitions_order1),
            'transitions_order2_count': len(transitions_order2)
        }

    # ============================================================================
    # FORMULES 3-5: PROBABILITÉS CONJOINTES ET MARGINALES
    # ============================================================================

    def compute_joint_probability(self, trajectory: List[str],
                                initial_distribution: Optional[np.ndarray] = None) -> float:
        """
        FORMULE 3: Loi conjointe fini-dimensionnelle

        Calcule ℙ_{ρ_0}(X_0=x_0, ..., X_n=x_n) = ρ_0(x_0) P_{x_0,x_1} ⋯ P_{x_{n-1},x_n}

        Args:
            trajectory: Séquence d'états [x_0, x_1, ..., x_n]
            initial_distribution: Distribution initiale ρ_0

        Returns:
            Probabilité conjointe de la trajectoire
        """
        if len(trajectory) == 0:
            return 0.0

        if initial_distribution is None:
            # Distribution uniforme par défaut
            initial_distribution = np.ones(len(self.states)) / len(self.states)

        transition_matrix = self.compute_transition_matrix()

        # Probabilité initiale ρ_0(x_0)
        x0_idx = self.state_to_idx[trajectory[0]]
        joint_prob = initial_distribution[x0_idx]

        # Produit des probabilités de transition
        for i in range(len(trajectory) - 1):
            current_idx = self.state_to_idx[trajectory[i]]
            next_idx = self.state_to_idx[trajectory[i + 1]]
            joint_prob *= transition_matrix[current_idx, next_idx]

        return joint_prob

    def compute_marginal_distribution(self, n: int,
                                    initial_distribution: Optional[np.ndarray] = None) -> np.ndarray:
        """
        FORMULE 4: Marginale n-ième

        Calcule ℙ_{ρ_0}(X_n=x_n) = (ρ_0 P^n)(x_n)

        Args:
            n: Temps n
            initial_distribution: Distribution initiale ρ_0

        Returns:
            Distribution marginale au temps n
        """
        if initial_distribution is None:
            # Distribution empirique basée sur les données
            sequence = self.get_sequence('index5')
            initial_distribution = np.zeros(len(self.states))
            if sequence:
                initial_state = sequence[0]
                initial_idx = self.state_to_idx[initial_state]
                initial_distribution[initial_idx] = 1.0
            else:
                initial_distribution = np.ones(len(self.states)) / len(self.states)

        transition_matrix = self.compute_transition_matrix()

        # Calcul de P^n
        P_n = np.linalg.matrix_power(transition_matrix, n)

        # Distribution marginale = ρ_0 * P^n
        marginal_distribution = initial_distribution @ P_n

        return marginal_distribution

    def compute_n_step_transition_probability(self, from_state: str, to_state: str, n: int) -> float:
        """
        FORMULE 5: Probabilité de transition n-étapes

        Calcule ℙ_x(X_n=y) = P^n(x,y)

        Args:
            from_state: État de départ x
            to_state: État d'arrivée y
            n: Nombre d'étapes

        Returns:
            Probabilité de transition en n étapes
        """
        transition_matrix = self.compute_transition_matrix()
        P_n = np.linalg.matrix_power(transition_matrix, n)

        from_idx = self.state_to_idx[from_state]
        to_idx = self.state_to_idx[to_state]

        return P_n[from_idx, to_idx]

    # ============================================================================
    # FORMULES 8-15: TEMPS D'ARRÊT ET RÉCURRENCE
    # ============================================================================

    def compute_first_return_times(self, target_state: str,
                                 sequence: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        FORMULES 8, 14: Temps de premier retour et probabilités de retour

        Calcule τ_x = inf{n ≥ 1 : X_n = x} et q = ℙ_x(τ_x < ∞)

        Args:
            target_state: État cible x
            sequence: Séquence d'observations

        Returns:
            Statistiques des temps de retour
        """
        if sequence is None:
            sequence = self.get_sequence('index5')

        return_times = []
        last_visit = None

        for i, state in enumerate(sequence):
            if state == target_state:
                if last_visit is not None:
                    return_time = i - last_visit
                    return_times.append(return_time)
                last_visit = i

        if not return_times:
            return {
                'mean_return_time': float('inf'),
                'return_probability': 0.0,
                'return_times': [],
                'is_recurrent': False
            }

        mean_return_time = np.mean(return_times)
        return_probability = len(return_times) / len([i for i, s in enumerate(sequence) if s == target_state])

        # Test de récurrence: état récurrent si probabilité de retour = 1
        is_recurrent = return_probability > 0.95  # Seuil empirique

        return {
            'mean_return_time': mean_return_time,
            'return_probability': return_probability,
            'return_times': return_times,
            'is_recurrent': is_recurrent,
            'visits_count': len([i for i, s in enumerate(sequence) if s == target_state])
        }

    # ============================================================================
    # FORMULES 16-25: ANALYSE SPECTRALE ET CONVERGENCE
    # ============================================================================

    def compute_spectral_analysis(self) -> Dict[str, Any]:
        """
        FORMULES 16-25: Analyse spectrale de la matrice de transition

        Calcule les valeurs propres, vecteurs propres, et propriétés de convergence
        """
        transition_matrix = self.compute_transition_matrix()

        # Calcul des valeurs propres et vecteurs propres
        eigenvalues, eigenvectors = np.linalg.eig(transition_matrix.T)

        # Tri par valeur propre décroissante
        idx = np.argsort(np.real(eigenvalues))[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]

        # Distribution stationnaire (vecteur propre associé à λ=1)
        stationary_vector = np.real(eigenvectors[:, 0])
        stationary_vector = stationary_vector / np.sum(stationary_vector)

        # Taux de convergence (deuxième plus grande valeur propre en module)
        convergence_rate = np.abs(eigenvalues[1]) if len(eigenvalues) > 1 else 0.0

        # Temps de mélange (approximation)
        if convergence_rate > 0 and convergence_rate < 1:
            mixing_time = int(np.ceil(-np.log(0.25) / np.log(convergence_rate)))
        else:
            mixing_time = float('inf')

        return {
            'eigenvalues': eigenvalues,
            'eigenvectors': eigenvectors,
            'stationary_distribution': stationary_vector,
            'convergence_rate': convergence_rate,
            'mixing_time': mixing_time,
            'is_irreducible': np.all(np.real(eigenvalues[0]) > np.real(eigenvalues[1:])),
            'spectral_gap': 1 - convergence_rate
        }

    # ============================================================================
    # FORMULES 35-36: THÉORÈME ERGODIQUE
    # ============================================================================

    def verify_ergodic_theorem(self, function_values: Dict[str, float],
                             sequence: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        FORMULE 35: Théorème ergodique pour chaînes de Markov

        Vérifie que lim_{n→∞} 1/n ∑_{k=0}^n f(X_k) = 𝔼_π[f]

        Args:
            function_values: Valeurs de la fonction f pour chaque état
            sequence: Séquence d'observations

        Returns:
            Résultats de vérification du théorème ergodique
        """
        if sequence is None:
            sequence = self.get_sequence('index5')

        # Calcul de la moyenne empirique
        empirical_averages = []
        cumulative_sum = 0.0

        for i, state in enumerate(sequence):
            cumulative_sum += function_values.get(state, 0.0)
            empirical_average = cumulative_sum / (i + 1)
            empirical_averages.append(empirical_average)

        # Calcul de l'espérance théorique sous la distribution stationnaire
        spectral_results = self.compute_spectral_analysis()
        stationary_dist = spectral_results['stationary_distribution']

        theoretical_expectation = sum(stationary_dist[self.state_to_idx[state]] * value
                                    for state, value in function_values.items()
                                    if state in self.state_to_idx)

        # Test de convergence
        final_empirical = empirical_averages[-1] if empirical_averages else 0.0
        convergence_error = abs(final_empirical - theoretical_expectation)

        return {
            'empirical_averages': empirical_averages,
            'theoretical_expectation': theoretical_expectation,
            'final_empirical_average': final_empirical,
            'convergence_error': convergence_error,
            'convergence_achieved': convergence_error < 0.05,
            'sequence_length': len(sequence)
        }

    # ============================================================================
    # FORMULES 61-70: ENTROPIE ET THÉORIE DE L'INFORMATION
    # ============================================================================

    def compute_entropy_analysis(self) -> Dict[str, Any]:
        """
        FORMULES 61-70: Analyse entropique complète

        Calcule l'entropie de Shannon, le taux d'entropie, et l'évolution entropique
        """
        transition_matrix = self.compute_transition_matrix()
        spectral_results = self.compute_spectral_analysis()
        stationary_dist = spectral_results['stationary_distribution']

        # Entropie de Shannon de la distribution stationnaire
        shannon_entropy = -np.sum(stationary_dist * np.log2(stationary_dist + 1e-15))

        # Taux d'entropie (entropie conditionnelle)
        entropy_rate = 0.0
        for i, pi in enumerate(stationary_dist):
            if pi > 0:
                row_entropy = -np.sum(transition_matrix[i, :] *
                                    np.log2(transition_matrix[i, :] + 1e-15))
                entropy_rate += pi * row_entropy

        # Entropie maximale (distribution uniforme)
        max_entropy = np.log2(len(self.states))

        # Efficacité entropique
        entropy_efficiency = shannon_entropy / max_entropy if max_entropy > 0 else 0.0

        # Information mutuelle entre états consécutifs
        sequence = self.get_sequence('index5')
        mutual_information = self._compute_mutual_information(sequence)

        return {
            'shannon_entropy': shannon_entropy,
            'entropy_rate': entropy_rate,
            'max_entropy': max_entropy,
            'entropy_efficiency': entropy_efficiency,
            'redundancy': max_entropy - shannon_entropy,
            'mutual_information': mutual_information,
            'stationary_distribution': stationary_dist
        }

    def _compute_mutual_information(self, sequence: List[str]) -> float:
        """Calcule l'information mutuelle entre X_n et X_{n+1}"""
        if len(sequence) < 2:
            return 0.0

        # Distributions marginales
        states_n = sequence[:-1]
        states_n1 = sequence[1:]

        # Comptage des occurrences
        count_n = {}
        count_n1 = {}
        count_joint = {}

        for i in range(len(states_n)):
            state_n = states_n[i]
            state_n1 = states_n1[i]

            count_n[state_n] = count_n.get(state_n, 0) + 1
            count_n1[state_n1] = count_n1.get(state_n1, 0) + 1
            count_joint[(state_n, state_n1)] = count_joint.get((state_n, state_n1), 0) + 1

        total = len(states_n)
        mutual_info = 0.0

        for (state_n, state_n1), joint_count in count_joint.items():
            p_joint = joint_count / total
            p_n = count_n[state_n] / total
            p_n1 = count_n1[state_n1] / total

            if p_joint > 0 and p_n > 0 and p_n1 > 0:
                mutual_info += p_joint * np.log2(p_joint / (p_n * p_n1))

        return mutual_info

    # ============================================================================
    # MÉTHODES D'ANALYSE GLOBALE
    # ============================================================================

    def analyze_single_hand(self, hand_index: int) -> Dict[str, Any]:
        """
        Analyse probabiliste d'une main individuelle

        Args:
            hand_index: Index de la main à analyser

        Returns:
            Analyse complète de la main
        """
        if hand_index >= len(self.hands):
            raise ValueError(f"Index {hand_index} hors limites (max: {len(self.hands)-1})")

        hand = self.hands[hand_index]

        # Probabilités de transition depuis l'état de cette main
        transition_matrix = self.compute_transition_matrix()
        current_state_idx = self.state_to_idx[hand.index5]
        transition_probs = transition_matrix[current_state_idx, :]

        # Analyse des cartes
        total_cards = len(hand.cartes_player) + len(hand.cartes_banker)
        card_values_player = [card['valeur'] for card in hand.cartes_player]
        card_values_banker = [card['valeur'] for card in hand.cartes_banker]

        return {
            'hand_info': {
                'main_number': hand.main_number,
                'manche_pb_number': hand.manche_pb_number,
                'index5': hand.index5,
                'result': hand.index3,
                'scores': (hand.score_player, hand.score_banker)
            },
            'transition_probabilities': {
                state: prob for state, prob in zip(self.states, transition_probs)
            },
            'card_analysis': {
                'total_cards': total_cards,
                'player_cards_values': card_values_player,
                'banker_cards_values': card_values_banker,
                'player_card_sum': sum(card_values_player),
                'banker_card_sum': sum(card_values_banker)
            },
            'state_classification': {
                'index1': hand.index1,
                'index2': hand.index2,
                'index3': hand.index3
            }
        }

    def analyze_sequence_up_to_n(self, n: int) -> Dict[str, Any]:
        """
        Analyse probabiliste de toutes les mains de 1 à n

        Args:
            n: Numéro de la main finale (incluse)

        Returns:
            Analyse complète de la séquence
        """
        if n > len(self.hands):
            raise ValueError(f"n={n} dépasse le nombre de mains disponibles ({len(self.hands)})")

        # Séquence des états jusqu'à la main n
        sequence = [hand.index5 for hand in self.hands[:n]]

        # Analyse de la matrice de transition sur cette sous-séquence
        transition_matrix = self.compute_transition_matrix(sequence)

        # Analyse spectrale
        spectral_results = self.compute_spectral_analysis()

        # Analyse entropique
        entropy_results = self.compute_entropy_analysis()

        # Vérification de la propriété de Markov
        markov_verification = self.verify_markov_property(sequence)

        # Analyse des temps de retour pour chaque état
        return_times_analysis = {}
        for state in self.states:
            if state in sequence:
                return_times_analysis[state] = self.compute_first_return_times(state, sequence)

        return {
            'sequence_info': {
                'length': n,
                'unique_states': len(set(sequence)),
                'state_sequence': sequence,
                'final_state': sequence[-1] if sequence else None
            },
            'transition_analysis': {
                'transition_matrix': transition_matrix,
                'matrix_properties': {
                    'is_stochastic': np.allclose(transition_matrix.sum(axis=1), 1.0),
                    'is_doubly_stochastic': np.allclose(transition_matrix.sum(axis=0), 1.0),
                    'rank': np.linalg.matrix_rank(transition_matrix)
                }
            },
            'spectral_analysis': spectral_results,
            'entropy_analysis': entropy_results,
            'markov_verification': markov_verification,
            'return_times_analysis': return_times_analysis
        }

    def delimit_parties(self) -> Dict[int, List[BaccaratHand]]:
        """
        Délimite les parties basées sur manche_pb_number

        Returns:
            Dictionnaire {partie_id: [mains]}
        """
        parties = {}
        for hand in self.hands:
            partie_id = hand.manche_pb_number
            if partie_id not in parties:
                parties[partie_id] = []
            parties[partie_id].append(hand)

        return parties

    def analyze_single_partie(self, partie_id: int) -> Dict[str, Any]:
        """
        Analyse probabiliste d'une partie complète

        Args:
            partie_id: ID de la partie (manche_pb_number)

        Returns:
            Analyse complète de la partie
        """
        parties = self.delimit_parties()

        if partie_id not in parties:
            raise ValueError(f"Partie {partie_id} non trouvée")

        partie_hands = parties[partie_id]
        partie_sequence = [hand.index5 for hand in partie_hands]

        # Analyse spécifique à cette partie
        if len(partie_sequence) > 1:
            # Matrice de transition pour cette partie
            transition_matrix = self._compute_transition_matrix_for_sequence(partie_sequence)

            # Analyse entropique
            entropy_analysis = self._compute_entropy_for_sequence(partie_sequence)

            # Probabilités de trajectoire
            trajectory_prob = self.compute_joint_probability(partie_sequence)
        else:
            transition_matrix = None
            entropy_analysis = None
            trajectory_prob = 0.0

        return {
            'partie_info': {
                'partie_id': partie_id,
                'nombre_mains': len(partie_hands),
                'sequence': partie_sequence,
                'main_numbers': [hand.main_number for hand in partie_hands],
                'resultats': [hand.index3 for hand in partie_hands]
            },
            'transition_analysis': {
                'transition_matrix': transition_matrix,
                'trajectory_probability': trajectory_prob
            },
            'entropy_analysis': entropy_analysis,
            'statistical_summary': {
                'player_wins': sum(1 for hand in partie_hands if hand.index3 == 'PLAYER'),
                'banker_wins': sum(1 for hand in partie_hands if hand.index3 == 'BANKER'),
                'ties': sum(1 for hand in partie_hands if hand.index3 == 'TIE'),
                'index1_distribution': {
                    '0': sum(1 for hand in partie_hands if hand.index1 == 0),
                    '1': sum(1 for hand in partie_hands if hand.index1 == 1)
                },
                'index2_distribution': {
                    'A': sum(1 for hand in partie_hands if hand.index2 == 'A'),
                    'B': sum(1 for hand in partie_hands if hand.index2 == 'B'),
                    'C': sum(1 for hand in partie_hands if hand.index2 == 'C')
                }
            }
        }

    def _compute_transition_matrix_for_sequence(self, sequence: List[str]) -> Optional[np.ndarray]:
        """Calcule la matrice de transition pour une séquence spécifique"""
        if len(sequence) < 2:
            return None

        # États présents dans cette séquence
        unique_states = list(set(sequence))
        n_states = len(unique_states)

        if n_states < 2:
            return None

        state_to_local_idx = {state: i for i, state in enumerate(unique_states)}

        # Compter les transitions
        transition_counts = np.zeros((n_states, n_states))

        for i in range(len(sequence) - 1):
            current_state = sequence[i]
            next_state = sequence[i + 1]

            current_idx = state_to_local_idx[current_state]
            next_idx = state_to_local_idx[next_state]

            transition_counts[current_idx, next_idx] += 1

        # Normalisation
        transition_matrix = np.zeros((n_states, n_states))
        for i in range(n_states):
            row_sum = transition_counts[i, :].sum()
            if row_sum > 0:
                transition_matrix[i, :] = transition_counts[i, :] / row_sum
            else:
                transition_matrix[i, :] = 1.0 / n_states

        return transition_matrix

    def _compute_entropy_for_sequence(self, sequence: List[str]) -> Dict[str, float]:
        """Calcule l'entropie pour une séquence spécifique"""
        if len(sequence) == 0:
            return {'shannon_entropy': 0.0, 'entropy_rate': 0.0}

        # Distribution des états
        unique_states, counts = np.unique(sequence, return_counts=True)
        probabilities = counts / len(sequence)

        # Entropie de Shannon
        shannon_entropy = -np.sum(probabilities * np.log2(probabilities + 1e-15))

        # Taux d'entropie (approximation)
        if len(sequence) > 1:
            transition_matrix = self._compute_transition_matrix_for_sequence(sequence)
            if transition_matrix is not None:
                entropy_rate = 0.0
                for i, prob in enumerate(probabilities):
                    if prob > 0:
                        row_entropy = -np.sum(transition_matrix[i, :] *
                                            np.log2(transition_matrix[i, :] + 1e-15))
                        entropy_rate += prob * row_entropy
            else:
                entropy_rate = 0.0
        else:
            entropy_rate = 0.0

        return {
            'shannon_entropy': shannon_entropy,
            'entropy_rate': entropy_rate,
            'max_entropy': np.log2(len(unique_states)) if len(unique_states) > 0 else 0.0
        }

    def analyze_all_parties(self) -> Dict[str, Any]:
        """
        Analyse complète de toutes les parties

        Returns:
            Analyse globale de toutes les parties
        """
        parties = self.delimit_parties()

        # Analyse de chaque partie
        parties_analysis = {}
        for partie_id in sorted(parties.keys()):
            parties_analysis[partie_id] = self.analyze_single_partie(partie_id)

        # Statistiques globales
        total_hands = len(self.hands)
        total_parties = len(parties)

        # Distribution des longueurs de parties
        partie_lengths = [len(hands) for hands in parties.values()]

        # Analyse de la séquence complète
        full_sequence_analysis = self.analyze_sequence_up_to_n(total_hands)

        return {
            'global_statistics': {
                'total_hands': total_hands,
                'total_parties': total_parties,
                'average_hands_per_partie': np.mean(partie_lengths),
                'min_hands_per_partie': min(partie_lengths),
                'max_hands_per_partie': max(partie_lengths),
                'partie_length_distribution': {
                    'lengths': partie_lengths,
                    'std': np.std(partie_lengths)
                }
            },
            'parties_analysis': parties_analysis,
            'full_sequence_analysis': full_sequence_analysis,
            'comparative_analysis': {
                'most_frequent_state': max(set(self.get_sequence()),
                                         key=self.get_sequence().count),
                'state_frequencies': {
                    state: self.get_sequence().count(state)
                    for state in self.states
                },
                'unique_states_count': len(self.states)
            }
        }

    # ============================================================================
    # MÉTHODES DE VISUALISATION ET RAPPORT
    # ============================================================================

    def generate_comprehensive_report(self) -> str:
        """
        Génère un rapport complet d'analyse

        Returns:
            Rapport formaté en texte
        """
        analysis = self.analyze_all_parties()

        report = []
        report.append("=" * 80)
        report.append("RAPPORT D'ANALYSE PROBABILISTE AVANCÉE - PARTIES DE BACCARAT")
        report.append("=" * 80)
        report.append(f"Date d'analyse : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Fichier analysé : {self.json_file_path}")
        report.append("")

        # Statistiques globales
        global_stats = analysis['global_statistics']
        report.append("1. STATISTIQUES GLOBALES")
        report.append("-" * 40)
        report.append(f"Nombre total de mains : {global_stats['total_hands']}")
        report.append(f"Nombre total de parties : {global_stats['total_parties']}")
        report.append(f"Moyenne mains/partie : {global_stats['average_hands_per_partie']:.2f}")
        report.append(f"Min-Max mains/partie : {global_stats['min_hands_per_partie']}-{global_stats['max_hands_per_partie']}")
        report.append("")

        # Analyse des états
        comp_analysis = analysis['comparative_analysis']
        report.append("2. ANALYSE DES ÉTATS MARKOVIENS")
        report.append("-" * 40)
        report.append(f"Nombre d'états uniques : {comp_analysis['unique_states_count']}")
        report.append(f"État le plus fréquent : {comp_analysis['most_frequent_state']}")
        report.append("")
        report.append("Distribution des états :")
        for state, freq in sorted(comp_analysis['state_frequencies'].items()):
            percentage = (freq / global_stats['total_hands']) * 100
            report.append(f"  {state}: {freq} occurrences ({percentage:.1f}%)")
        report.append("")

        # Analyse spectrale globale
        spectral = analysis['full_sequence_analysis']['spectral_analysis']
        report.append("3. ANALYSE SPECTRALE GLOBALE")
        report.append("-" * 40)
        report.append(f"Taux de convergence : {spectral['convergence_rate']:.4f}")
        report.append(f"Temps de mélange : {spectral['mixing_time']}")
        report.append(f"Gap spectral : {spectral['spectral_gap']:.4f}")
        report.append(f"Chaîne irréductible : {spectral['is_irreducible']}")
        report.append("")

        # Analyse entropique globale
        entropy = analysis['full_sequence_analysis']['entropy_analysis']
        report.append("4. ANALYSE ENTROPIQUE GLOBALE")
        report.append("-" * 40)
        report.append(f"Entropie de Shannon : {entropy['shannon_entropy']:.4f} bits")
        report.append(f"Taux d'entropie : {entropy['entropy_rate']:.4f} bits")
        report.append(f"Entropie maximale : {entropy['max_entropy']:.4f} bits")
        report.append(f"Efficacité entropique : {entropy['entropy_efficiency']:.4f}")
        report.append(f"Information mutuelle : {entropy['mutual_information']:.4f} bits")
        report.append("")

        # Vérification propriété de Markov
        markov = analysis['full_sequence_analysis']['markov_verification']
        report.append("5. VÉRIFICATION PROPRIÉTÉ DE MARKOV")
        report.append("-" * 40)
        report.append(f"Statistique χ² : {markov['chi2_statistic']:.4f}")
        report.append(f"Degrés de liberté : {markov['degrees_freedom']}")
        report.append(f"p-value : {markov['p_value']:.6f}")
        report.append(f"Propriété satisfaite : {markov['markov_property_satisfied']}")
        report.append("")

        # Résumé des parties
        report.append("6. RÉSUMÉ DES PARTIES")
        report.append("-" * 40)

        player_wins_total = 0
        banker_wins_total = 0
        ties_total = 0

        for partie_id, partie_analysis in analysis['parties_analysis'].items():
            stats = partie_analysis['statistical_summary']
            player_wins_total += stats['player_wins']
            banker_wins_total += stats['banker_wins']
            ties_total += stats['ties']

        total_games = player_wins_total + banker_wins_total + ties_total
        report.append(f"Victoires PLAYER : {player_wins_total} ({player_wins_total/total_games*100:.1f}%)")
        report.append(f"Victoires BANKER : {banker_wins_total} ({banker_wins_total/total_games*100:.1f}%)")
        report.append(f"Égalités (TIE) : {ties_total} ({ties_total/total_games*100:.1f}%)")
        report.append("")

        report.append("=" * 80)
        report.append("FIN DU RAPPORT")
        report.append("=" * 80)

        return "\n".join(report)

    def save_analysis_to_file(self, output_filename: str = "analyse_baccarat_markov.txt"):
        """
        Sauvegarde l'analyse complète dans un fichier

        Args:
            output_filename: Nom du fichier de sortie
        """
        report = self.generate_comprehensive_report()

        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"Analyse sauvegardée dans : {output_filename}")


# ============================================================================
# EXEMPLE D'UTILISATION
# ============================================================================

def main():
    """Fonction principale d'exemple"""

    # Initialisation de l'analyseur
    analyzer = BaccaratMarkovAnalyzer("exemple.json")

    print("🎰 ANALYSEUR PROBABILISTE BACCARAT - CHAÎNES DE MARKOV")
    print("=" * 60)

    # Analyse d'une main spécifique
    print("\n📊 ANALYSE D'UNE MAIN SPÉCIFIQUE (Main #10)")
    hand_analysis = analyzer.analyze_single_hand(9)  # Index 9 = Main 10
    print(f"État : {hand_analysis['hand_info']['index5']}")
    print(f"Résultat : {hand_analysis['hand_info']['result']}")
    print(f"Scores : {hand_analysis['hand_info']['scores']}")

    # Analyse d'une séquence jusqu'à la main n
    print("\n📈 ANALYSE SÉQUENCE JUSQU'À LA MAIN 20")
    sequence_analysis = analyzer.analyze_sequence_up_to_n(20)
    print(f"États uniques : {sequence_analysis['sequence_info']['unique_states']}")
    print(f"État final : {sequence_analysis['sequence_info']['final_state']}")

    # Analyse d'une partie spécifique
    print("\n🎯 ANALYSE D'UNE PARTIE SPÉCIFIQUE (Partie #1)")
    partie_analysis = analyzer.analyze_single_partie(1)
    print(f"Nombre de mains : {partie_analysis['partie_info']['nombre_mains']}")
    print(f"Résultats : {partie_analysis['partie_info']['resultats']}")

    # Génération du rapport complet
    print("\n📋 GÉNÉRATION DU RAPPORT COMPLET...")
    analyzer.save_analysis_to_file("rapport_analyse_baccarat_markov.txt")

    print("\n✅ ANALYSE TERMINÉE")


if __name__ == "__main__":
    main()
